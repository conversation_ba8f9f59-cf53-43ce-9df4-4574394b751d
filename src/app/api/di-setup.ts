/**
 * Dependency Injection Setup
 * Khởi tạo và cấu hình tất cả dependencies cho container
 */

import { container, SERVICE_IDENTIFIERS } from "./di-container";
import { CacheService } from "./services/cache.service";
import {
  CACHE_SERVICE,
  USER_SERVICE,
  PRODUCT_SERVICE,
  CATEGORY_SERVICE,
  ORDER_SERVICE,
  CART_SERVICE,
  BRAND_SERVICE,
  ADDRESS_SERVICE,
  REVIEW_SERVICE,
  WISHLIST_SERVICE,
  MEDIA_SERVICE,
  MENU_SERVICE,
  ADMIN_USER_SERVICE,
  SETTING_SERVICE,
  NOTIFICATION_SERVICE,
  AUDIT_LOG_SERVICE,
  CONTACT_SERVICE,
  ATTRIBUTE_SERVICE,
  INVENTORY_SERVICE,
  PROMOTION_SERVICE,
  POST_SERVICE,
  PAGE_SERVICE,
  EVENT_SERVICE,
  NOTIFICATION_HANDLERS_SERVICE,
  WEBSOCKET_SERVICE,
  EMAIL_SERVICE,
  METRICS_SERVICE,
  RATE_LIMIT_SERVICE,
  // Repository identifiers
  USER_REPOSITORY,
  ADMIN_USER_REPOSITORY,
  PRODUCT_REPOSITORY,
  CATEGORY_REPOSITORY,
  BRAND_REPOSITORY,
  ORDER_REPOSITORY,
  CART_REPOSITORY,
  ADDRESS_REPOSITORY,
  REVIEW_REPOSITORY,
  WISHLIST_REPOSITORY,
  POST_REPOSITORY,
  PAGE_REPOSITORY,
  MEDIA_REPOSITORY,
  MENU_REPOSITORY,
  SETTING_REPOSITORY,
  NOTIFICATION_REPOSITORY,
  AUDIT_LOG_REPOSITORY,
  CONTACT_REPOSITORY,
  ATTRIBUTE_REPOSITORY,
  INVENTORY_REPOSITORY,
  PROMOTION_REPOSITORY,
} from "./services/service-identifiers";

// Import repositories
import {
  UserRepository,
  AdminUserRepository,
  ProductRepository,
  CategoryRepository,
  BrandRepository,
  OrderRepository,
  CartRepository,
  AddressRepository,
  ReviewRepository,
  WishlistRepository,
  PostRepository,
  PageRepository,
  MediaRepository,
  MenuRepository,
  SettingRepository,
  NotificationRepository,
  AuditLogRepository,
  ContactRepository,
  AttributeRepository,
  InventoryRepository,
  PromotionRepository,
} from "./repositories";

// Import services (classes only, identifiers from service-identifiers.ts)
import { UserService } from "./services/user.service";
import { ProductService } from "./services/product.service";
import { CategoryService } from "./services/category.service";
import { OrderService } from "./services/order.service";
import { CartService } from "./services/cart.service";
import { BrandService } from "./services/brand.service";
import { AddressService } from "./services/address.service";
import { ReviewService } from "./services/review.service";
import { WishlistService } from "./services/wishlist.service";
import { MediaService } from "./services/media.service";
import { MenuService } from "./services/menu.service";
import { AdminUserService } from "./services/admin-user.service";
import { SettingService } from "./services/setting.service";
import { NotificationService } from "./services/notification.service";
import { AuditLogService } from "./services/audit-log.service";
import { PostService } from "./services/post.service";
import { PageService } from "./services/page.service";
import { ContactService } from "./services/contact.service";
import { AttributeService } from "./services/attribute.service";
import { InventoryService } from "./services/inventory.service";
import { PromotionService } from "./services/promotion.service";
import { EventService } from "./services/event.service";
import { NotificationHandlersService } from "./services/notification-handlers.service";
import { WebSocketService } from "./services/websocket.service";
import { EmailService } from "./services/email.service";
import { MetricsService } from "./services/metrics.service";
import { RateLimitService } from "./services/rate-limit.service";

/**
 * Setup tất cả repositories trong DI container
 */
export function setupRepositories(): void {
  // Core repositories
  container.register(USER_REPOSITORY, () => new UserRepository());

  container.register(ADMIN_USER_REPOSITORY, () => new AdminUserRepository());

  container.register(PRODUCT_REPOSITORY, () => new ProductRepository());

  container.register(CATEGORY_REPOSITORY, () => new CategoryRepository());

  container.register(BRAND_REPOSITORY, () => new BrandRepository());

  container.register(ORDER_REPOSITORY, () => new OrderRepository());

  // E-commerce repositories
  container.register(CART_REPOSITORY, () => new CartRepository());

  container.register(ADDRESS_REPOSITORY, () => new AddressRepository());

  container.register(REVIEW_REPOSITORY, () => new ReviewRepository());

  container.register(WISHLIST_REPOSITORY, () => new WishlistRepository());

  // Content repositories
  container.register(POST_REPOSITORY, () => new PostRepository());

  container.register(PAGE_REPOSITORY, () => new PageRepository());

  container.register(MEDIA_REPOSITORY, () => new MediaRepository());

  container.register(MENU_REPOSITORY, () => new MenuRepository());

  // System repositories
  container.register(SETTING_REPOSITORY, () => new SettingRepository());

  container.register(
    NOTIFICATION_REPOSITORY,
    () => new NotificationRepository()
  );

  container.register(AUDIT_LOG_REPOSITORY, () => new AuditLogRepository());

  container.register(CONTACT_REPOSITORY, () => new ContactRepository());

  // Advanced repositories
  container.register(ATTRIBUTE_REPOSITORY, () => new AttributeRepository());

  container.register(INVENTORY_REPOSITORY, () => new InventoryRepository());

  container.register(PROMOTION_REPOSITORY, () => new PromotionRepository());
}

/**
 * Setup tất cả services trong DI container
 */
export function setupServices(): void {
  // Cache Service (register first as other services may depend on it)
  container.register(CACHE_SERVICE, () => new CacheService(), {
    singleton: true,
  });

  // User Service
  container.register(USER_SERVICE, () => new UserService(), {
    singleton: true,
  });

  // Product Service
  container.register(PRODUCT_SERVICE, () => new ProductService(), {
    singleton: true,
  });

  // Category Service
  container.register(CATEGORY_SERVICE, () => new CategoryService(), {
    singleton: true,
  });

  // Order Service
  container.register(ORDER_SERVICE, () => new OrderService(), {
    singleton: true,
  });

  // Cart Service
  container.register(CART_SERVICE, () => new CartService(), {
    singleton: true,
  });

  // Brand Service
  container.register(BRAND_SERVICE, () => new BrandService(), {
    singleton: true,
  });

  // Address Service
  container.register(ADDRESS_SERVICE, () => new AddressService(), {
    singleton: true,
  });

  // Review Service
  container.register(
    REVIEW_SERVICE,
    () =>
      new ReviewService(
        container.resolve(REVIEW_REPOSITORY),
        container.resolve(PRODUCT_REPOSITORY),
        container.resolve(USER_REPOSITORY),
        container.resolve(ORDER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Wishlist Service
  container.register(
    WISHLIST_SERVICE,
    () =>
      new WishlistService(
        container.resolve(WISHLIST_REPOSITORY),
        container.resolve(PRODUCT_REPOSITORY),
        container.resolve(USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Media Service
  container.register(MEDIA_SERVICE, () => new MediaService(), {
    singleton: true,
  });

  // Menu Service
  container.register(MENU_SERVICE, () => new MenuService(), {
    singleton: true,
  });

  // Admin User Service
  container.register(ADMIN_USER_SERVICE, () => new AdminUserService(), {
    singleton: true,
  });

  // Setting Service
  container.register(
    SETTING_SERVICE,
    () => new SettingService(container.resolve(SETTING_REPOSITORY)),
    {
      singleton: true,
    }
  );

  // Notification Service
  container.register(
    NOTIFICATION_SERVICE,
    () =>
      new NotificationService(
        container.resolve(SERVICE_IDENTIFIERS.NOTIFICATION_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    {
      singleton: true,
    }
  );

  // Audit Log Service
  container.register(
    AUDIT_LOG_SERVICE,
    () =>
      new AuditLogService(
        container.resolve(SERVICE_IDENTIFIERS.AUDIT_LOG_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Post Service
  container.register(
    POST_SERVICE,
    () =>
      new PostService(
        container.resolve(SERVICE_IDENTIFIERS.POST_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY)
      ),
    { singleton: true }
  );

  // Page Service
  container.register(
    PAGE_SERVICE,
    () =>
      new PageService(container.resolve(SERVICE_IDENTIFIERS.PAGE_REPOSITORY)),
    { singleton: true }
  );

  // Contact Service
  container.register(
    CONTACT_SERVICE,
    () =>
      new ContactService(
        container.resolve(SERVICE_IDENTIFIERS.CONTACT_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Attribute Service
  container.register(
    ATTRIBUTE_SERVICE,
    () =>
      new AttributeService(
        container.resolve(SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY)
      ),
    { singleton: true }
  );

  // Inventory Service
  container.register(
    INVENTORY_SERVICE,
    () =>
      new InventoryService(
        container.resolve(SERVICE_IDENTIFIERS.INVENTORY_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY)
      ),
    { singleton: true }
  );

  // Promotion Service
  container.register(
    PROMOTION_SERVICE,
    () =>
      new PromotionService(
        container.resolve(SERVICE_IDENTIFIERS.PROMOTION_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY),
        container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY)
      ),
    { singleton: true }
  );

  // Event and Communication Services
  container.register(EVENT_SERVICE, () => EventService.getInstance(), {
    singleton: true,
  });

  container.register(
    NOTIFICATION_HANDLERS_SERVICE,
    () => NotificationHandlersService,
    { singleton: true }
  );

  container.register(WEBSOCKET_SERVICE, () => WebSocketService.getInstance(), {
    singleton: true,
  });

  container.register(EMAIL_SERVICE, () => EmailService.getInstance(), {
    singleton: true,
  });

  container.register(METRICS_SERVICE, () => MetricsService.getInstance(), {
    singleton: true,
  });

  container.register(RATE_LIMIT_SERVICE, () => RateLimitService.getInstance(), {
    singleton: true,
  });

  console.log("All services registered successfully");
}

/**
 * Khởi tạo toàn bộ DI container
 */
export function initializeDI(): void {
  console.log("Initializing Dependency Injection Container...");

  // Setup repositories
  setupRepositories();

  // Setup services
  setupServices();

  console.log("DI Container initialized successfully");
  console.log("Registered services:", container.getRegisteredServices().length);
}

/**
 * Helper functions để lấy repositories từ container
 */
export const getRepositories = () => ({
  userRepository: container.resolve(SERVICE_IDENTIFIERS.USER_REPOSITORY),
  adminUserRepository: container.resolve(
    SERVICE_IDENTIFIERS.ADMIN_USER_REPOSITORY
  ),
  productRepository: container.resolve(SERVICE_IDENTIFIERS.PRODUCT_REPOSITORY),
  categoryRepository: container.resolve(
    SERVICE_IDENTIFIERS.CATEGORY_REPOSITORY
  ),
  brandRepository: container.resolve(SERVICE_IDENTIFIERS.BRAND_REPOSITORY),
  orderRepository: container.resolve(SERVICE_IDENTIFIERS.ORDER_REPOSITORY),
  cartRepository: container.resolve(SERVICE_IDENTIFIERS.CART_REPOSITORY),
  addressRepository: container.resolve(SERVICE_IDENTIFIERS.ADDRESS_REPOSITORY),
  reviewRepository: container.resolve(SERVICE_IDENTIFIERS.REVIEW_REPOSITORY),
  wishlistRepository: container.resolve(
    SERVICE_IDENTIFIERS.WISHLIST_REPOSITORY
  ),
  postRepository: container.resolve(SERVICE_IDENTIFIERS.POST_REPOSITORY),
  pageRepository: container.resolve(SERVICE_IDENTIFIERS.PAGE_REPOSITORY),
  mediaRepository: container.resolve(SERVICE_IDENTIFIERS.MEDIA_REPOSITORY),
  menuRepository: container.resolve(SERVICE_IDENTIFIERS.MENU_REPOSITORY),
  settingRepository: container.resolve(SERVICE_IDENTIFIERS.SETTING_REPOSITORY),
  notificationRepository: container.resolve(
    SERVICE_IDENTIFIERS.NOTIFICATION_REPOSITORY
  ),
  auditLogRepository: container.resolve(
    SERVICE_IDENTIFIERS.AUDIT_LOG_REPOSITORY
  ),
  contactRepository: container.resolve(SERVICE_IDENTIFIERS.CONTACT_REPOSITORY),
  attributeRepository: container.resolve(
    SERVICE_IDENTIFIERS.ATTRIBUTE_REPOSITORY
  ),
  inventoryRepository: container.resolve(
    SERVICE_IDENTIFIERS.INVENTORY_REPOSITORY
  ),
  promotionRepository: container.resolve(
    SERVICE_IDENTIFIERS.PROMOTION_REPOSITORY
  ),
});

/**
 * Helper functions để lấy services từ container
 */
export const getServices = () => ({
  userService: container.resolve<UserService>(USER_SERVICE),
  productService: container.resolve<ProductService>(PRODUCT_SERVICE),
  categoryService: container.resolve<CategoryService>(CATEGORY_SERVICE),
  orderService: container.resolve<OrderService>(ORDER_SERVICE),
  cartService: container.resolve<CartService>(CART_SERVICE),
  brandService: container.resolve<BrandService>(BRAND_SERVICE),
  addressService: container.resolve<AddressService>(ADDRESS_SERVICE),
  reviewService: container.resolve<ReviewService>(REVIEW_SERVICE),
  wishlistService: container.resolve<WishlistService>(WISHLIST_SERVICE),
  mediaService: container.resolve<MediaService>(MEDIA_SERVICE),
  menuService: container.resolve<MenuService>(MENU_SERVICE),
  adminUserService: container.resolve<AdminUserService>(ADMIN_USER_SERVICE),
  settingService: container.resolve<SettingService>(SETTING_SERVICE),
  notificationService:
    container.resolve<NotificationService>(NOTIFICATION_SERVICE),
  auditLogService: container.resolve<AuditLogService>(AUDIT_LOG_SERVICE),
  postService: container.resolve<PostService>(POST_SERVICE),
  pageService: container.resolve<PageService>(PAGE_SERVICE),
  contactService: container.resolve<ContactService>(CONTACT_SERVICE),
  attributeService: container.resolve<AttributeService>(ATTRIBUTE_SERVICE),
  inventoryService: container.resolve<InventoryService>(INVENTORY_SERVICE),
  promotionService: container.resolve<PromotionService>(PROMOTION_SERVICE),
  eventService: container.resolve<EventService>(EVENT_SERVICE),
  notificationHandlersService: container.resolve<NotificationHandlersService>(
    NOTIFICATION_HANDLERS_SERVICE
  ),
  webSocketService: container.resolve<WebSocketService>(WEBSOCKET_SERVICE),
  emailService: container.resolve<EmailService>(EMAIL_SERVICE),
  metricsService: container.resolve<MetricsService>(METRICS_SERVICE),
  rateLimitService: container.resolve<RateLimitService>(RATE_LIMIT_SERVICE),
});

// Auto-initialize DI container khi import module này
if (typeof window === "undefined") {
  // Chỉ initialize trên server side
  initializeDI();
}
