import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { container } from "@/app/api/di-container";
import { CONTACT_SERVICE } from "@/app/api/services/service-identifiers";
import type { ContactService } from "@/app/api/services/contact.service";
import { verifyAdminAuth } from "@/lib/auth/admin-auth";

const contactUpdateSchema = z.object({
  status: z
    .enum([
      "NEW",
      "OPEN",
      "IN_PROGRESS",
      "WAITING_CUSTOMER",
      "RESOLVED",
      "CLOSED",
      "SPAM",
    ])
    .optional(),
  priority: z.enum(["LOW", "NORMAL", "HIGH", "URGENT"]).optional(),
  assignedTo: z.string().nullable().optional(),
  responseMessage: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// GET /api/admin/contacts/[id] - Get contact by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await verifyAdminAuth();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const contactService = container.resolve<ContactService>(CONTACT_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const contact = await contactService.getContactById(
      params.id,
      adminUserEntity
    );

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    return NextResponse.json(contact);
  } catch (error) {
    console.error("Error fetching contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/contacts/[id] - Update contact
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await verifyAdminAuth();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const updateData = contactUpdateSchema.parse(body);

    const contactService = container.resolve<ContactService>(CONTACT_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const updatedContact = await contactService.updateContact(
      params.id,
      {
        ...updateData,
        status: updateData.status as any,
        priority: updateData.priority as any,
        assignedTo: updateData.assignedTo || undefined,
      },
      adminUserEntity
    );

    return NextResponse.json(updatedContact);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid update data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/contacts/[id] - Delete contact
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await verifyAdminAuth();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const contactService = container.resolve<ContactService>(CONTACT_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Delete contact (this will cascade delete notes)
    await contactService.deleteContact(params.id, adminUserEntity);

    return NextResponse.json(
      { message: "Contact deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
