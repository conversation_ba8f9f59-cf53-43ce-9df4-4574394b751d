import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { withAudit, auditConfigs } from "@/lib/audit-middleware";
import { triggerLowStockAlert } from "@/lib/notification-rules";
import { container } from "../../../di-container";
import {
  AUDIT_LOG_SERVICE,
  PRODUCT_SERVICE,
} from "../../../services/service-identifiers";
import { AuditLogService } from "../../../services/audit-log.service";
import { ProductService } from "../../../services/product.service";
import { initializeSystem } from "../../../initialize";

interface RouteParams {
  params: {
    id: string;
  };
}

// Example of how to integrate audit logging into product operations

// GET with audit logging (for sensitive data access)
const auditedGET = withAudit("VIEW", "Product", {
  getResourceId: (request: NextRequest, params: any) => params?.id,
  getDescription: (request: NextRequest, params: any) =>
    `Viewed product details: ${params?.id}`,
  skipLogging: (_request: NextRequest, _params: any) => {
    // Skip logging for non-sensitive operations
    return false;
  },
})(async (request: NextRequest, context: any) => {
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Initialize system and get services
  initializeSystem();
  const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

  const { id } = context.params;

  const product = await productService.getProductById(id);

  if (!product) {
    return NextResponse.json({ error: "Product not found" }, { status: 404 });
  }

  return NextResponse.json({ data: product });
});

// PUT with audit logging (for updates)
const auditedPUT = withAudit(
  "UPDATE",
  "Product",
  auditConfigs.updateProduct
)(async (request: NextRequest, context: any) => {
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Initialize system and get services
  initializeSystem();
  const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

  const { id } = context.params;
  const body = await request.json();

  // Get old values for audit
  const oldProduct = await productService.getProductById(id);

  if (!oldProduct) {
    return NextResponse.json({ error: "Product not found" }, { status: 404 });
  }

  // Update product
  const updatedProduct = await productService.updateProduct(id, body, {
    id: "admin",
    role: "admin",
    type: "admin",
  } as any);

  // Check for low stock and trigger notification
  if (updatedProduct.stock <= 10 && oldProduct.stock > 10) {
    await triggerLowStockAlert(
      updatedProduct.id,
      updatedProduct.name,
      updatedProduct.stock
    );
  }

  return NextResponse.json({
    message: "Product updated successfully",
    data: updatedProduct,
  });
});

// DELETE with audit logging
const auditedDELETE = withAudit(
  "DELETE",
  "Product",
  auditConfigs.deleteProduct
)(async (request: NextRequest, context: any) => {
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Only ADMIN can delete products
  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  // Initialize system and get services
  initializeSystem();
  const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

  const { id } = context.params;

  // Check if product exists
  const product = await productService.getProductById(id);

  if (!product) {
    return NextResponse.json({ error: "Product not found" }, { status: 404 });
  }

  // Check if product has orders - simplified check
  // Note: This would need a proper service method to check order count

  // Delete product
  await productService.deleteProduct(id, {
    id: "admin",
    role: "admin",
    type: "admin",
  } as any);

  return NextResponse.json({
    message: "Product deleted successfully",
  });
});

// Export the audited handlers
export { auditedGET as GET, auditedPUT as PUT, auditedDELETE as DELETE };

// Example of manual audit logging for complex operations
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Initialize system and get services
    initializeSystem();
    const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

    const { id } = params;
    const body = await request.json();
    const { action } = body;

    // Get current product state
    const currentProduct = await productService.getProductById(id);

    if (!currentProduct) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    let result;
    let auditDescription = "";

    switch (action) {
      case "toggle_featured":
        result = await productService.updateProduct(
          id,
          { featured: !currentProduct.featured },
          {
            id: "admin",
            role: "admin",
            type: "admin",
          } as any
        );
        auditDescription = `Toggled featured status to ${result.featured}`;
        break;

      case "update_stock":
        const { stock } = body;
        const oldStock = currentProduct.stock;

        result = await productService.updateProduct(
          id,
          { stock: parseInt(stock) },
          {
            id: "admin",
            role: "admin",
            type: "admin",
          } as any
        );

        auditDescription = `Updated stock from ${oldStock} to ${stock}`;

        // Trigger low stock notification if needed
        if (stock <= 10 && oldStock > 10) {
          await triggerLowStockAlert(id, currentProduct.name, stock);
        }
        break;

      case "change_status":
        const { status } = body;
        result = await productService.updateProduct(id, { status }, {
          id: "admin",
          role: "admin",
          type: "admin",
        } as any);
        auditDescription = `Changed status from ${currentProduct.status} to ${status}`;
        break;

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    // Use audit service for complex operations
    initializeSystem();
    const auditLogService =
      container.resolve<AuditLogService>(AUDIT_LOG_SERVICE);

    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: session.user.role || "ADMIN",
    };

    await auditLogService.createAuditLog({
      action: "UPDATE",
      resource: "Product",
      resourceId: id,
      adminId: adminUser.id,
      oldValues: {
        [action]: currentProduct[action as keyof typeof currentProduct],
      },
      newValues: { [action]: result[action as keyof typeof result] },
      metadata: { description: auditDescription },
      ipAddress: request.headers.get("x-forwarded-for") || "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
    });

    return NextResponse.json({
      message: "Product updated successfully",
      data: result,
    });
  } catch (error) {
    console.error("Product PATCH error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Example of batch operations with audit logging
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { action, productIds } = body;

    if (!Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { error: "Product IDs are required" },
        { status: 400 }
      );
    }

    initializeSystem();
    const auditLogService =
      container.resolve<AuditLogService>(AUDIT_LOG_SERVICE);
    const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

    const adminUser = {
      id: session.user.id,
      email: session.user.email || "",
      name: session.user.name || "",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: session.user.role || "ADMIN",
    };

    let result;
    let auditDescription = "";

    switch (action) {
      case "bulk_delete":
        // Only ADMIN can bulk delete
        if (session.user.role !== "ADMIN") {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }

        // Get products before deletion for audit - simplified
        // Note: This would need proper bulk operations in ProductService
        const productsToDelete = [];
        for (const productId of productIds) {
          try {
            const product = await productService.getProductById(productId);
            if (product) {
              productsToDelete.push({
                id: product.id,
                name: product.name,
                sku: product.sku,
              });
            }
          } catch {
            // Product not found, skip
          }
        }

        // Bulk delete - simplified to individual deletes
        let deleteCount = 0;
        for (const productId of productIds) {
          try {
            await productService.deleteProduct(productId, {
              id: "admin",
              role: "admin",
              type: "admin",
            } as any);
            deleteCount++;
          } catch {
            // Product not found or error, skip
          }
        }

        result = { count: deleteCount };

        auditDescription = `Bulk deleted ${result.count} products: ${productsToDelete.map((p) => p.name).join(", ")}`;

        // Log each deletion individually for better tracking
        for (const product of productsToDelete) {
          await auditLogService.createAuditLog({
            action: "DELETE",
            resource: "Product",
            resourceId: product.id,
            adminId: adminUser.id,
            metadata: {
              description: `Bulk deleted product: ${product.name} (${product.sku})`,
            },
            ipAddress: request.headers.get("x-forwarded-for") || "unknown",
            userAgent: request.headers.get("user-agent") || "unknown",
          });
        }
        break;

      case "bulk_update_status":
        const { status } = body;

        // Bulk update - simplified to individual updates
        let updateCount = 0;
        for (const productId of productIds) {
          try {
            await productService.updateProduct(productId, { status }, {
              id: "admin",
              role: "admin",
              type: "admin",
            } as any);
            updateCount++;
          } catch {
            // Product not found or error, skip
          }
        }

        result = { count: updateCount };

        auditDescription = `Bulk updated status to ${status} for ${result.count} products`;

        await auditLogService.createAuditLog({
          action: "BULK_UPDATE",
          resource: "Product",
          adminId: adminUser.id,
          metadata: { description: auditDescription },
          newValues: { productIds, newStatus: status },
          ipAddress: request.headers.get("x-forwarded-for") || "unknown",
          userAgent: request.headers.get("user-agent") || "unknown",
        });
        break;

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    return NextResponse.json({
      message: "Bulk operation completed successfully",
      affected: result.count,
    });
  } catch (error) {
    console.error("Product bulk operation error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
